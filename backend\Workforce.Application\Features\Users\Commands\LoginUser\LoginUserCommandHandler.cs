using AutoMapper;
using MediatR;
using Microsoft.Extensions.Configuration;
using Workforce.Application.Services;
using Workforce.Domain.Repositories;
using Workforce.Shared.DTOs;

namespace Workforce.Application.Features.Users.Commands.LoginUser
{
    /// <summary>
    /// Handler for LoginUserCommand
    /// </summary>
    public class LoginUserCommandHandler : IRequestHandler<LoginUserCommand, AuthenticatedUserDto>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IPasswordHashingService _passwordHashingService;
        private readonly IJwtTokenService _jwtTokenService;
        private readonly IAuditLoggingService _auditLoggingService;
        private readonly IRateLimitingService _rateLimitingService;
        private readonly IConfiguration _configuration;

        // Security configuration
        private readonly int _maxFailedAttempts;
        private readonly int _lockoutDurationMinutes;

        public LoginUserCommandHandler(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            IPasswordHashingService passwordHashingService,
            IJwtTokenService jwtTokenService,
            IAuditLoggingService auditLoggingService,
            IRateLimitingService rateLimitingService,
            IConfiguration configuration)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _passwordHashingService = passwordHashingService;
            _jwtTokenService = jwtTokenService;
            _auditLoggingService = auditLoggingService;
            _rateLimitingService = rateLimitingService;
            _configuration = configuration;

            _maxFailedAttempts = int.TryParse(_configuration["SecuritySettings:AccountLockout:MaxFailedAttempts"], out var maxAttempts) ? maxAttempts : 5;
            _lockoutDurationMinutes = int.TryParse(_configuration["SecuritySettings:AccountLockout:LockoutDurationMinutes"], out var lockoutDuration) ? lockoutDuration : 15;
        }

        /// <summary>
        /// Handles the user login command
        /// </summary>
        /// <param name="request">The login command</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Authenticated user DTO with tokens</returns>
        public async Task<AuthenticatedUserDto> Handle(LoginUserCommand request, CancellationToken cancellationToken)
        {
            var email = request.Email.ToLowerInvariant().Trim();

            // Check rate limits
            await CheckRateLimitsAsync(request.IpAddress, email, request.UserAgent);

            // Record login attempt for rate limiting
            if (!string.IsNullOrWhiteSpace(request.IpAddress))
                await _rateLimitingService.RecordIpLoginAttemptAsync(request.IpAddress);
            await _rateLimitingService.RecordEmailLoginAttemptAsync(email);

            // Get user by email
            var user = await _unitOfWork.Users.GetByEmailAsync(email);
            if (user == null)
            {
                await _auditLoggingService.LogFailedLoginAsync(email, "User not found", request.IpAddress, request.UserAgent);
                throw new UnauthorizedAccessException("Invalid email or password");
            }

            // Check if account is locked out
            if (user.IsLockedOut)
            {
                await _auditLoggingService.LogFailedLoginAsync(email, "Account locked out", request.IpAddress, request.UserAgent);
                throw new UnauthorizedAccessException($"Account is locked out until {user.LockoutEnd:yyyy-MM-dd HH:mm:ss} UTC");
            }

            // Check if account is active
            if (!user.IsActive)
            {
                await _auditLoggingService.LogFailedLoginAsync(email, "Account inactive", request.IpAddress, request.UserAgent);
                throw new UnauthorizedAccessException("Account is inactive");
            }

            // Verify password
            var isPasswordValid = _passwordHashingService.VerifyPassword(user.PasswordHash, request.Password);
            if (!isPasswordValid)
            {
                await HandleFailedLoginAsync(user, email, request.IpAddress, request.UserAgent);
                throw new UnauthorizedAccessException("Invalid email or password");
            }

            // Reset failed login attempts on successful login
            if (user.FailedLoginAttempts > 0)
            {
                await _unitOfWork.Users.ResetFailedLoginAttemptsAsync(user.UserId);
            }

            // Update last login timestamp
            await _unitOfWork.Users.UpdateLastLoginAsync(user.UserId, DateTime.UtcNow);

            // Generate JWT tokens
            var accessToken = _jwtTokenService.GenerateAccessToken(user);
            var refreshToken = _jwtTokenService.GenerateRefreshToken();
            var expirationSeconds = _jwtTokenService.GetTokenExpirationSeconds();

            // Map user to DTO
            var userProfileDto = _mapper.Map<UserProfileDto>(user);

            // Log successful login
            await _auditLoggingService.LogSuccessfulLoginAsync(user.UserId, email, request.IpAddress, request.UserAgent);

            // Create the authenticated user response
            var authenticatedUserDto = new AuthenticatedUserDto
            {
                User = userProfileDto,
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                TokenType = "Bearer",
                ExpiresIn = expirationSeconds,
                ExpiresAt = DateTime.UtcNow.AddSeconds(expirationSeconds)
            };

            return authenticatedUserDto;
        }

        /// <summary>
        /// Checks rate limits for IP and email
        /// </summary>
        private async Task CheckRateLimitsAsync(string? ipAddress, string email, string? userAgent)
        {
            // Check IP rate limit
            if (!string.IsNullOrWhiteSpace(ipAddress))
            {
                var isIpLimited = await _rateLimitingService.IsIpRateLimitExceededAsync(ipAddress);
                if (isIpLimited)
                {
                    var resetTime = await _rateLimitingService.GetIpRateLimitResetTimeAsync(ipAddress);
                    await _auditLoggingService.LogRateLimitExceededAsync(ipAddress, "IP", ipAddress, userAgent);
                    throw new InvalidOperationException($"Too many login attempts from this IP address. Try again in {resetTime.TotalMinutes:F0} minutes.");
                }
            }

            // Check email rate limit
            var isEmailLimited = await _rateLimitingService.IsEmailRateLimitExceededAsync(email);
            if (isEmailLimited)
            {
                var resetTime = await _rateLimitingService.GetEmailRateLimitResetTimeAsync(email);
                await _auditLoggingService.LogRateLimitExceededAsync(email, "Email", ipAddress, userAgent);
                throw new InvalidOperationException($"Too many login attempts for this email. Try again in {resetTime.TotalMinutes:F0} minutes.");
            }
        }

        /// <summary>
        /// Handles failed login attempts and potential account lockout
        /// </summary>
        private async Task HandleFailedLoginAsync(Domain.Entities.User user, string email, string? ipAddress, string? userAgent)
        {
            // Increment failed login attempts
            await _unitOfWork.Users.IncrementFailedLoginAttemptsAsync(user.UserId);

            // Check if account should be locked out
            var newFailedAttempts = user.FailedLoginAttempts + 1;
            if (newFailedAttempts >= _maxFailedAttempts)
            {
                var lockoutEnd = DateTime.UtcNow.AddMinutes(_lockoutDurationMinutes);
                await _unitOfWork.Users.LockoutUserAsync(user.UserId, lockoutEnd);
                await _auditLoggingService.LogAccountLockoutAsync(user.UserId, email, lockoutEnd, ipAddress, userAgent);
                await _auditLoggingService.LogFailedLoginAsync(email, "Invalid password - Account locked", ipAddress, userAgent);
            }
            else
            {
                await _auditLoggingService.LogFailedLoginAsync(email, "Invalid password", ipAddress, userAgent);
            }
        }
    }
}
