using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;

namespace Workforce.Application.Services
{
    /// <summary>
    /// Service for rate limiting operations using in-memory cache
    /// </summary>
    public class RateLimitingService : IRateLimitingService
    {
        private readonly IMemoryCache _cache;
        private readonly int _ipMaxAttempts;
        private readonly int _emailMaxAttempts;
        private readonly TimeSpan _ipWindowDuration;
        private readonly TimeSpan _emailWindowDuration;

        public RateLimitingService(IMemoryCache cache, IConfiguration configuration)
        {
            _cache = cache;
            _ipMaxAttempts = int.TryParse(configuration["SecuritySettings:RateLimit:IpMaxAttempts"], out var ipMax) ? ipMax : 10;
            _emailMaxAttempts = int.TryParse(configuration["SecuritySettings:RateLimit:EmailMaxAttempts"], out var emailMax) ? emailMax : 5;
            _ipWindowDuration = TimeSpan.FromMinutes(int.TryParse(configuration["SecuritySettings:RateLimit:IpWindowMinutes"], out var ipWindow) ? ipWindow : 1);
            _emailWindowDuration = TimeSpan.FromMinutes(int.TryParse(configuration["SecuritySettings:RateLimit:EmailWindowMinutes"], out var emailWindow) ? emailWindow : 1);
        }

        /// <summary>
        /// Checks if the IP address has exceeded the rate limit for login attempts
        /// </summary>
        /// <param name="ipAddress">The IP address to check</param>
        /// <returns>True if rate limit is exceeded, false otherwise</returns>
        public async Task<bool> IsIpRateLimitExceededAsync(string ipAddress)
        {
            var key = $"ip_login_attempts:{ipAddress?.Replace(":", "_").Replace(".", "_")}";
            var attempts = _cache.Get<List<DateTime>>(key) ?? new List<DateTime>();
            
            // Remove expired attempts
            var cutoff = DateTime.UtcNow.Subtract(_ipWindowDuration);
            attempts = attempts.Where(a => a > cutoff).ToList();
            
            return await Task.FromResult(attempts.Count >= _ipMaxAttempts);
        }

        /// <summary>
        /// Checks if the email has exceeded the rate limit for login attempts
        /// </summary>
        /// <param name="email">The email to check</param>
        /// <returns>True if rate limit is exceeded, false otherwise</returns>
        public async Task<bool> IsEmailRateLimitExceededAsync(string email)
        {
            var key = $"email_login_attempts:{email.ToLowerInvariant()}";
            var attempts = _cache.Get<List<DateTime>>(key) ?? new List<DateTime>();
            
            // Remove expired attempts
            var cutoff = DateTime.UtcNow.Subtract(_emailWindowDuration);
            attempts = attempts.Where(a => a > cutoff).ToList();
            
            return await Task.FromResult(attempts.Count >= _emailMaxAttempts);
        }

        /// <summary>
        /// Records a login attempt for IP-based rate limiting
        /// </summary>
        /// <param name="ipAddress">The IP address</param>
        public async Task RecordIpLoginAttemptAsync(string ipAddress)
        {
            var key = $"ip_login_attempts:{ipAddress}";
            var attempts = _cache.Get<List<DateTime>>(key) ?? new List<DateTime>();
            
            // Remove expired attempts
            var cutoff = DateTime.UtcNow.Subtract(_ipWindowDuration);
            attempts = attempts.Where(a => a > cutoff).ToList();
            
            // Add current attempt
            attempts.Add(DateTime.UtcNow);
            
            // Cache with expiration
            _cache.Set(key, attempts, _ipWindowDuration);
            
            await Task.CompletedTask;
        }

        /// <summary>
        /// Records a login attempt for email-based rate limiting
        /// </summary>
        /// <param name="email">The email address</param>
        public async Task RecordEmailLoginAttemptAsync(string email)
        {
            var key = $"email_login_attempts:{email.ToLowerInvariant()}";
            var attempts = _cache.Get<List<DateTime>>(key) ?? new List<DateTime>();
            
            // Remove expired attempts
            var cutoff = DateTime.UtcNow.Subtract(_emailWindowDuration);
            attempts = attempts.Where(a => a > cutoff).ToList();
            
            // Add current attempt
            attempts.Add(DateTime.UtcNow);
            
            // Cache with expiration
            _cache.Set(key, attempts, _emailWindowDuration);
            
            await Task.CompletedTask;
        }

        /// <summary>
        /// Gets the remaining time until the IP rate limit resets
        /// </summary>
        /// <param name="ipAddress">The IP address</param>
        /// <returns>Time remaining until reset</returns>
        public async Task<TimeSpan> GetIpRateLimitResetTimeAsync(string ipAddress)
        {
            var key = $"ip_login_attempts:{ipAddress}";
            var attempts = _cache.Get<List<DateTime>>(key) ?? new List<DateTime>();
            
            if (attempts.Count == 0)
                return TimeSpan.Zero;
            
            var oldestAttempt = attempts.Min();
            var resetTime = oldestAttempt.Add(_ipWindowDuration);
            var remaining = resetTime.Subtract(DateTime.UtcNow);
            
            return await Task.FromResult(remaining > TimeSpan.Zero ? remaining : TimeSpan.Zero);
        }

        /// <summary>
        /// Gets the remaining time until the email rate limit resets
        /// </summary>
        /// <param name="email">The email address</param>
        /// <returns>Time remaining until reset</returns>
        public async Task<TimeSpan> GetEmailRateLimitResetTimeAsync(string email)
        {
            var key = $"email_login_attempts:{email.ToLowerInvariant()}";
            var attempts = _cache.Get<List<DateTime>>(key) ?? new List<DateTime>();
            
            if (attempts.Count == 0)
                return TimeSpan.Zero;
            
            var oldestAttempt = attempts.Min();
            var resetTime = oldestAttempt.Add(_emailWindowDuration);
            var remaining = resetTime.Subtract(DateTime.UtcNow);
            
            return await Task.FromResult(remaining > TimeSpan.Zero ? remaining : TimeSpan.Zero);
        }
    }
}
